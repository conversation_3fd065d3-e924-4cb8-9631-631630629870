using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Xaml;
using System.ComponentModel;
using System.Globalization;

namespace DrawnUi.Draw;

/// <summary>
/// Simple theme-aware value converter for DrawnUI controls
/// Usage: TextColor="{Binding Source={x:Static Application.Current}, Path=RequestedTheme, Converter={StaticResource ThemeToColorConverter}, ConverterParameter='Red|Blue'}"
/// </summary>
public class ThemeToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (parameter is string paramStr && paramStr.Contains('|'))
        {
            var parts = paramStr.Split('|');
            if (parts.Length >= 2)
            {
                var lightValue = parts[0].Trim();
                var darkValue = parts[1].Trim();

                var theme = Application.Current?.RequestedTheme ?? AppTheme.Unspecified;
                var userTheme = Application.Current?.UserAppTheme ?? AppTheme.Unspecified;

                var effectiveTheme = userTheme != AppTheme.Unspecified ? userTheme : theme;

                var selectedValue = effectiveTheme == AppTheme.Dark ? darkValue : lightValue;

                // Try to convert to Color if target type is Color
                if (targetType == typeof(Color))
                {
                    if (Color.TryParse(selectedValue, out var color))
                        return color;
                }

                // Try to convert to double for FontSize
                if (targetType == typeof(double))
                {
                    if (double.TryParse(selectedValue, out var doubleValue))
                        return doubleValue;
                }

                return selectedValue;
            }
        }

        return parameter?.ToString() ?? "Transparent";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Custom theme binding extension for DrawnUI controls that works similar to AppThemeBinding
/// but is compatible with SkiaControl and other DrawnUI elements
/// </summary>
[ContentProperty(nameof(Default))]
public class ThemeBindingExtension : IMarkupExtension<object>
{
    public object Light { get; set; }
    public object Dark { get; set; }
    public object Default { get; set; }

    public object ProvideValue(IServiceProvider serviceProvider)
    {
        if (serviceProvider == null)
            return Default;

        try
        {
            // Get the target object and property
            var targetProvider = serviceProvider.GetService<IProvideValueTarget>();
            if (targetProvider?.TargetObject == null || targetProvider.TargetProperty == null)
                return GetCurrentThemeValue();

            var targetObject = targetProvider.TargetObject;
            var targetProperty = targetProvider.TargetProperty;

            // Create a theme-aware binding that will update when theme changes
            if (targetObject is BindableObject bindableObject && targetProperty is BindableProperty bindableProperty)
            {
                // Create and register a theme binding
                var themeBinding = new SkiaThemeBinding(this, bindableObject, bindableProperty);
                
                // Set initial value
                var initialValue = GetCurrentThemeValue();
                return initialValue;
            }

            return GetCurrentThemeValue();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SkiaThemeBinding error: {ex.Message}");
            return Default;
        }
    }

    public object GetCurrentThemeValue()
    {
        var currentTheme = Application.Current?.RequestedTheme ?? AppTheme.Unspecified;
        var userTheme = Application.Current?.UserAppTheme ?? AppTheme.Unspecified;

        // Use user theme if set, otherwise use system theme
        var effectiveTheme = userTheme != AppTheme.Unspecified ? userTheme : currentTheme;

        return effectiveTheme switch
        {
            AppTheme.Dark => Dark ?? Default,
            AppTheme.Light => Light ?? Default,
            _ => Default
        };
    }

    object IMarkupExtension.ProvideValue(IServiceProvider serviceProvider)
    {
        return ProvideValue(serviceProvider);
    }
}

/// <summary>
/// Internal class to manage theme change notifications for a specific binding
/// </summary>
internal class SkiaThemeBinding : IDisposable
{
    private readonly ThemeBindingExtension _extension;
    private readonly WeakReference<BindableObject> _targetObjectRef;
    private readonly BindableProperty _targetProperty;
    private bool _disposed = false;

    public SkiaThemeBinding(ThemeBindingExtension extension, BindableObject targetObject, BindableProperty targetProperty)
    {
        _extension = extension;
        _targetObjectRef = new WeakReference<BindableObject>(targetObject);
        _targetProperty = targetProperty;

        // Subscribe to theme changes
        if (Application.Current != null)
        {
            Application.Current.PropertyChanged += OnApplicationPropertyChanged;
        }

        // Register this binding for cleanup when target is disposed
        SkiaThemeBindingManager.RegisterBinding(this);
    }

    private void OnApplicationPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(Application.RequestedTheme) || 
            e.PropertyName == nameof(Application.UserAppTheme))
        {
            UpdateTargetValue();
        }
    }

    public void UpdateTargetValue()
    {
        if (_disposed || !_targetObjectRef.TryGetTarget(out var targetObject))
        {
            Dispose();
            return;
        }

        try
        {
            var newValue = _extension.GetCurrentThemeValue();
            targetObject.SetValue(_targetProperty, newValue);

            // For DrawnUI controls, also trigger a repaint
            if (targetObject is DrawnUi.Draw.SkiaControl skiaControl)
            {
                skiaControl.InvalidateViewport();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SkiaThemeBinding update error: {ex.Message}");
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        if (Application.Current != null)
        {
            Application.Current.PropertyChanged -= OnApplicationPropertyChanged;
        }

        SkiaThemeBindingManager.UnregisterBinding(this);
    }
}

/// <summary>
/// Manager to keep track of active theme bindings and clean up disposed ones
/// </summary>
internal static class SkiaThemeBindingManager
{
    private static readonly List<WeakReference<SkiaThemeBinding>> _bindings = new();
    private static readonly object _lock = new object();

    public static void RegisterBinding(SkiaThemeBinding binding)
    {
        lock (_lock)
        {
            _bindings.Add(new WeakReference<SkiaThemeBinding>(binding));
            
            // Cleanup dead references periodically
            if (_bindings.Count % 50 == 0)
            {
                CleanupDeadReferences();
            }
        }
    }

    public static void UnregisterBinding(SkiaThemeBinding binding)
    {
        lock (_lock)
        {
            for (int i = _bindings.Count - 1; i >= 0; i--)
            {
                if (!_bindings[i].TryGetTarget(out var target) || target == binding)
                {
                    _bindings.RemoveAt(i);
                }
            }
        }
    }

    private static void CleanupDeadReferences()
    {
        for (int i = _bindings.Count - 1; i >= 0; i--)
        {
            if (!_bindings[i].TryGetTarget(out _))
            {
                _bindings.RemoveAt(i);
            }
        }
    }

    public static void ForceUpdateAllBindings()
    {
        lock (_lock)
        {
            foreach (var bindingRef in _bindings.ToList())
            {
                if (bindingRef.TryGetTarget(out var binding))
                {
                    try
                    {
                        binding.UpdateTargetValue();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Force update error: {ex.Message}");
                    }
                }
            }
        }
    }
}
