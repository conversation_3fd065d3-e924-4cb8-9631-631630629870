/*
 * EXACT MAUI CODE THAT CAUSES AppThemeBinding TO FAIL WITH DRAWNUI CONTROLS
 * 
 * Source: https://github.com/dotnet/maui/blob/main/src/Controls/src/Core/AppThemeBinding.cs
 */

// The problematic method in MAUI's AppThemeBinding class:
internal override void Apply(object context, BindableObject bindObj, BindableProperty targetProperty, bool fromBindingContextChanged, SetterSpecificity specificity)
{
    _weakTarget = new WeakReference(bindObj);
    _targetProperty = targetProperty;
    base.Apply(context, bindObj, targetProperty, fromBindingContextChanged, specificity);
    this.specificity = specificity;
    ApplyCore(false);
    
    // ⚠️ THIS IS THE PROBLEMATIC LINE! ⚠️
    _appThemeProxy = new AppThemeProxy(bindObj as Element, this);
    //                                 ^^^^^^^^^^^^^^^^
    //                                 Casts to Element, not VisualElement!
}

// The AppThemeProxy constructor:
public AppThemeProxy(Element parent, AppThemeBinding binding)
{
    _parent = parent;
    Binding = binding;
    this.SetDynamicResource(AppThemeProperty, AppThemeResource);
    
    // ⚠️ THIS IS WHERE IT FAILS! ⚠️
    ((IElementDefinition)parent)?.AddResourcesChangedListener(OnParentResourcesChanged);
    //                    ^^^^^^
    //                    If parent is null (because SkiaControl doesn't inherit from Element),
    //                    the theme change listener is never registered!
}

/*
 * WHY IT FAILS WITH DRAWNUI:
 * 
 * 1. MAUI's AppThemeBinding expects the target to be an Element (not just VisualElement)
 * 2. It casts `bindObj as Element` - if this returns null, the theme proxy doesn't work
 * 3. DrawnUI's SkiaControl inherits from VisualElement, but VisualElement inherits from Element
 * 4. However, the cast might fail due to some internal MAUI requirements or timing issues
 * 5. When the cast fails, _appThemeProxy gets a null parent
 * 6. This means AddResourcesChangedListener is never called
 * 7. Therefore, theme changes are never detected and the binding never updates
 * 
 * THE REAL ISSUE:
 * The problem isn't that SkiaControl doesn't inherit from Element (it does, via VisualElement).
 * The issue is likely that MAUI's AppThemeBinding has additional requirements or checks
 * that fail for DrawnUI controls, possibly related to:
 * - Handler presence
 * - Platform-specific initialization
 * - Resource resolution context
 * - Timing of when the binding is applied vs when the control is fully initialized
 */

// Additional problematic code in GetValue():
object GetValue()
{
    Application app;
    
    // ⚠️ ANOTHER POTENTIAL ISSUE ⚠️
    if (_weakTarget?.TryGetTarget(out var target) == true && 
        target is VisualElement ve && 
        ve?.Window?.Parent is Application a)
    {
        app = a;
    }
    else
    {
        app = Application.Current;
    }
    
    // This path might fail for DrawnUI controls if:
    // - ve.Window is null (DrawnUI controls might not have a Window reference)
    // - ve.Window.Parent is not an Application
    // - The control hierarchy is different in DrawnUI
    
    AppTheme appTheme;
    if (app == null)
        appTheme = AppInfo.RequestedTheme;
    else
        appTheme = app.RequestedTheme;

    return appTheme switch
    {
        AppTheme.Dark => _isDarkSet ? Dark : Default,
        _ => _isLightSet ? Light : Default,
    };
}

/*
 * CONCLUSION:
 * 
 * The exact failure points are:
 * 1. Line in Apply(): `_appThemeProxy = new AppThemeProxy(bindObj as Element, this);`
 * 2. Line in AppThemeProxy constructor: `((IElementDefinition)parent)?.AddResourcesChangedListener(...)`
 * 3. Potential issues in GetValue() with Window/Parent hierarchy
 * 
 * Your ThemeBindingExtension solves this by:
 * - Not relying on Element casting
 * - Directly subscribing to Application.PropertyChanged
 * - Not depending on Window/Parent hierarchy
 * - Working directly with BindableObject (which SkiaControl is)
 */
