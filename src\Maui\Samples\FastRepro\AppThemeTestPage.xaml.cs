using Microsoft.Maui.Controls;

namespace Sandbox;

public partial class AppThemeTestPage : ContentPage
{
    private bool _isDarkTheme = false;

    public AppThemeTestPage()
    {
        try
        {
            InitializeComponent();
            UpdateCurrentThemeDisplay();
        }
        catch (Exception e)
        {
            Super.DisplayException(this, e);
        }
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
        UpdateCurrentThemeDisplay();
    }

    private void OnThemeSwitchClicked(object sender, EventArgs e)
    {
        try
        {
            // Toggle theme
            _isDarkTheme = !_isDarkTheme;
            
            // Set the application theme
            Application.Current.UserAppTheme = _isDarkTheme ? AppTheme.Dark : AppTheme.Light;
            
            // Update button text
            ThemeSwitchButton.Text = _isDarkTheme ? "Switch to Light Theme" : "Switch to Dark Theme";
            
            // Update current theme display
            UpdateCurrentThemeDisplay();
            
            // Force a layout update to ensure theme changes are applied
            this.ForceLayout();
            
            // Optional: Add a small delay and force another update
            Device.BeginInvokeOnMainThread(async () =>
            {
                await Task.Delay(100);
                UpdateCurrentThemeDisplay();
                this.ForceLayout();
            });
        }
        catch (Exception ex)
        {
            Super.DisplayException(this, ex);
        }
    }

    private void UpdateCurrentThemeDisplay()
    {
        try
        {
            var currentTheme = Application.Current.RequestedTheme;
            var userTheme = Application.Current.UserAppTheme;
            
            string themeText = $"Current Theme: {currentTheme}";
            if (userTheme != AppTheme.Unspecified)
            {
                themeText += $" (User: {userTheme})";
            }
            
            CurrentThemeLabel.Text = themeText;
            
            // Update internal state to match actual theme
            _isDarkTheme = (userTheme == AppTheme.Dark) || 
                          (userTheme == AppTheme.Unspecified && currentTheme == AppTheme.Dark);
            
            // Update button text to reflect current state
            ThemeSwitchButton.Text = _isDarkTheme ? "Switch to Light Theme" : "Switch to Dark Theme";
        }
        catch (Exception ex)
        {
            CurrentThemeLabel.Text = $"Error getting theme: {ex.Message}";
        }
    }

    // Method to test programmatic theme binding updates
    private void TestProgrammaticUpdate()
    {
        try
        {
            // This method can be called to test if programmatic updates work
            // You can add a button to call this if needed
            
            if (DrawnTestLabel != null)
            {
                // Try to force update the DrawnUI label
                DrawnTestLabel.InvalidateViewport();
            }
        }
        catch (Exception ex)
        {
            Super.DisplayException(this, ex);
        }
    }
}
